// 简化的渲染器脚本
class SimpleApp {
  constructor() {
    this.initializeElements();
    this.setupEventListeners();
    this.setupIPC();
    
    this.addLog('系统已就绪，请输入邮箱和密码开始操作', 'info');
  }

  initializeElements() {
    // 获取DOM元素
    this.elements = {
      email: document.getElementById('email'),
      password: document.getElementById('password'),
      autoRegisterLoginBtn: document.getElementById('auto-register-login-btn'),
      oneClickBtn: document.getElementById('one-click-btn'),
      generateEmailBtn: document.getElementById('generate-email-btn'),
      logToggleBtn: document.getElementById('log-toggle-btn'),
      logPanel: document.getElementById('log-panel'),
      logPanelCloseBtn: document.getElementById('log-panel-close-btn'),
      status: document.getElementById('status'),
      progressFill: document.getElementById('progress-fill'),
      logContainer: document.getElementById('log-container')
    };
  }

  setupEventListeners() {
    // 自动注册+登录+订阅按钮
    this.elements.autoRegisterLoginBtn.addEventListener('click', () => {
      this.handleAutoRegisterLogin();
    });

    // 一键生成并执行按钮
    this.elements.oneClickBtn.addEventListener('click', () => {
      this.handleOneClickExecution();
    });

    // 自动生成邮箱按钮
    this.elements.generateEmailBtn.addEventListener('click', () => {
      this.handleGenerateEmail();
    });

    // 日志切换按钮
    this.elements.logToggleBtn.addEventListener('click', () => {
      this.toggleLogPanel();
    });

    // 日志面板关闭按钮
    this.elements.logPanelCloseBtn.addEventListener('click', () => {
      this.toggleLogPanel();
    });

    // 回车键支持
    [this.elements.email, this.elements.password].forEach(input => {
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.handleAutoRegisterLogin();
        }
      });
    });
  }

  setupIPC() {
    // 监听状态更新
    window.electronAPI?.onStatusUpdate((status) => {
      this.updateStatus(status, 'working');
    });

    // 监听操作结果
    window.electronAPI?.onOperationResult((result) => {
      this.handleOperationResult(result);
    });
  }

  handleAutoRegisterLogin() {
    const email = this.elements.email.value.trim();
    const password = this.elements.password.value.trim();

    if (!email || !password) {
      this.updateStatus('请输入邮箱和密码', 'error');
      this.addLog('❌ 请输入完整的邮箱和密码', 'error');
      return;
    }

    if (!this.isValidEmail(email)) {
      this.updateStatus('请输入有效的邮箱地址', 'error');
      this.addLog('❌ 邮箱格式不正确', 'error');
      return;
    }

    // 重置重试计数器
    window.electronAPI?.resetRetryCount();

    // 禁用按钮并开始操作
    this.setButtonState(false);
    this.updateStatus('正在执行自动注册+登录+订阅...', 'working');
    this.setProgress(10);
    
    this.addLog(`🚀 开始自动化流程: ${email}`, 'info');
    
    // 发送到主进程
    window.electronAPI?.autoRegisterLogin({
      email: email,
      password: password
    });
  }

  handleGenerateEmail() {
    const generatedEmail = this.generateRandomEmail();
    this.elements.email.value = generatedEmail;
    
    // 自动填充默认密码
    if (!this.elements.password.value.trim()) {
      this.elements.password.value = 'zhang123456';
    }
    
    this.addLog(`🎲 已生成邮箱: ${generatedEmail}`, 'info');
    this.updateStatus('邮箱已自动生成，可以开始操作', 'success');
    
    // 添加一个小动画效果
    this.elements.email.style.background = '#e8f5e8';
    setTimeout(() => {
      this.elements.email.style.background = '#fff';
    }, 1000);
  }

  handleOneClickExecution() {
    // 自动生成邮箱和密码
    const generatedEmail = this.generateRandomEmail();
    this.elements.email.value = generatedEmail;
    this.elements.password.value = 'zhang123456';
    
    this.addLog(`⚡ 一键执行: 已生成邮箱 ${generatedEmail}`, 'info');
    
    // 添加视觉反馈
    this.elements.email.style.background = '#e8f5e8';
    this.elements.password.style.background = '#e8f5e8';
    
    setTimeout(() => {
      this.elements.email.style.background = '#fff';
      this.elements.password.style.background = '#fff';
    }, 1000);
  
    // 延迟500ms后自动执行，让用户看到生成的邮箱
    setTimeout(() => {
      this.handleAutoRegisterLogin();
    }, 500);
  }

  toggleLogPanel() {
    const isVisible = this.elements.logPanel.classList.contains('show');
    if (isVisible) {
      // 隐藏日志面板
      this.elements.logPanel.classList.remove('show');
      this.elements.logToggleBtn.classList.remove('active');
      this.elements.logToggleBtn.textContent = '📋';
      this.elements.logToggleBtn.title = '查看日志';
      // 恢复按钮位置
      this.elements.logToggleBtn.style.right = '15px';
    } else {
      // 显示日志面板
      this.elements.logPanel.classList.add('show');
      this.elements.logToggleBtn.classList.add('active');
      this.elements.logToggleBtn.textContent = '📋';
      this.elements.logToggleBtn.title = '关闭日志';
      // 调整按钮位置，适配320px宽度的日志面板
      this.elements.logToggleBtn.style.right = '335px';
      
      // 自动滚动到底部
      setTimeout(() => {
        this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
      }, 300);
    }
  }

  generateRandomEmail() {
    // 获取或初始化邮箱计数器
    let emailCounter = localStorage.getItem('email_counter');
    if (!emailCounter) {
      emailCounter = Math.floor(Math.random() * 1000) + 100; // 随机起始数字
    } else {
      emailCounter = parseInt(emailCounter) + 1;
    }
    
    // 保存计数器
    localStorage.setItem('email_counter', emailCounter.toString());
    
    // 生成邮箱格式：cc{数字}@qq.com
    return `cc${emailCounter}@qq.com`;
}

  handleOperationResult(result) {
    this.addLog(`📋 操作结果: ${result.message}`, result.success ? 'success' : 'error');
    
    // 如果结果中包含Clash链接，显示可复制的链接
    if (result.success && result.finalData && result.finalData.clashLink) {
      this.displayClashLink(result.finalData.clashLink);
    }
    
    if (result.success) {
      this.updateStatus(result.message, 'success');
      this.setProgress(100);
    } else {
      this.updateStatus(result.message, 'error');
      this.setProgress(0);
    }

    // 重新启用按钮
    setTimeout(() => {
      this.setButtonState(true);
      this.setProgress(0);
    }, 3000);
  }

  displayClashLink(clashLink) {
    // 获取当前重试计数
    const retryCount = window.electronAPI?.getRetryCount() || 0;
    
    // 提取HTTP订阅链接用于手动导入
    const urlMatch = clashLink.match(/url=([^&]+)/);
    const httpSubscribeUrl = urlMatch ? decodeURIComponent(urlMatch[1]) : '';
    
    // 创建一个可复制的链接显示区域
    const linkContainer = document.createElement('div');
    linkContainer.className = 'clash-link-container';
    
    // 如果重试次数超过3次，显示手动导入选项
    if (retryCount >= 3) {
      linkContainer.innerHTML = `
        <div class="clash-link-header">⚠️ 自动打开失败，请手动导入</div>
        <div class="clash-link-content" onclick="navigator.clipboard.writeText('${httpSubscribeUrl}').then(() => this.textContent = '✅ 订阅链接已复制!')">
          ${httpSubscribeUrl}
        </div>
        <div class="clash-link-instructions">
          <div class="instruction-title">📋 手动导入步骤：</div>
          <div class="instruction-steps">
            <div>1. 点击上方链接复制订阅地址</div>
            <div>2. 打开Clash客户端</div>
            <div>3. 选择"配置" → "新建配置" → "从URL导入"</div>
            <div>4. 粘贴复制的链接并确认导入</div>
          </div>
        </div>
        <div class="clash-link-tip">💡 已尝试自动打开3次失败，请手动导入订阅配置</div>
      `;
    } else {
      linkContainer.innerHTML = `
        <div class="clash-link-header">🔗 Clash订阅链接 (点击复制)</div>
        <div class="clash-link-content" onclick="navigator.clipboard.writeText('${clashLink}').then(() => this.textContent = '✅ 链接已复制!')">
          ${clashLink}
        </div>
        <div class="clash-link-actions">
          <button class="retry-clash-btn" onclick="window.electronAPI?.retryOpenClash('${clashLink}')">
            🔄 重试打开Clash (${retryCount}/3)
          </button>
        </div>
        <div class="clash-link-instructions">
          <div class="instruction-title">📋 使用说明：</div>
          <div class="instruction-steps">
            <div>• 点击上方链接可复制到剪贴板</div>
            <div>• 如果没有自动弹出导入对话框，请点击"重试打开Clash"</div>
            <div>• 确保Clash客户端已启动并正确安装</div>
          </div>
        </div>
        <div class="clash-link-tip">💡 链接已自动发送给Clash客户端，请查看是否有导入提示</div>
      `;
    }
    
    this.elements.logContainer.appendChild(linkContainer);
    this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
    
    if (retryCount >= 3) {
      this.addLog('⚠️ 自动打开Clash失败3次，请手动导入订阅', 'warning');
  } else {
      this.addLog('🔗 Clash订阅链接已生成并自动打开', 'success');
    }
  }

  updateStatus(message, type = 'info') {
    this.elements.status.textContent = message;
    this.elements.status.className = `status ${type}`;
  }

  setProgress(percent) {
    this.elements.progressFill.style.width = `${percent}%`;
  }

  setButtonState(enabled) {
    this.elements.autoRegisterLoginBtn.disabled = !enabled;
    this.elements.oneClickBtn.disabled = !enabled;
    this.elements.generateEmailBtn.disabled = !enabled;
    
    this.elements.autoRegisterLoginBtn.textContent = enabled ? 
      '🎯 自动注册+登录+订阅' : '⏳ 执行中...';
    this.elements.oneClickBtn.textContent = enabled ? 
      '⚡ 一键生成并执行' : '⏳ 执行中...';
  }

  addLog(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    const time = new Date().toLocaleTimeString();
    const icon = this.getLogIcon(type);
    
    logEntry.innerHTML = `
      <span class="log-time">${time}</span>
      ${icon} ${message}
    `;
    
    this.elements.logContainer.appendChild(logEntry);
    this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
    
    // 限制日志条数
    if (this.elements.logContainer.children.length > 50) {
      this.elements.logContainer.removeChild(this.elements.logContainer.firstChild);
    }
  }

  getLogIcon(type) {
    const icons = {
      info: '📘',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };
    return icons[type] || '📘';
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new SimpleApp();
}); 