#!/bin/bash
echo "开始手动打包 Electron 应用..."

# 创建输出目录
mkdir -p dist/my-electron-app-win32-x64

echo "复制 Electron 运行时..."
cp -r node_modules/electron/dist/* dist/my-electron-app-win32-x64/

echo "创建应用目录..."
mkdir -p dist/my-electron-app-win32-x64/resources/app

echo "复制应用文件..."
cp index.js dist/my-electron-app-win32-x64/resources/app/
cp index.html dist/my-electron-app-win32-x64/resources/app/
cp preload.js dist/my-electron-app-win32-x64/resources/app/
cp renderer.js dist/my-electron-app-win32-x64/resources/app/
cp package.json dist/my-electron-app-win32-x64/resources/app/

echo "重命名可执行文件..."
mv dist/my-electron-app-win32-x64/electron.exe dist/my-electron-app-win32-x64/my-electron-app.exe

echo "打包完成！"
echo "可执行文件位置: dist/my-electron-app-win32-x64/my-electron-app.exe"
