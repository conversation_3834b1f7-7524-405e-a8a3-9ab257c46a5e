const { app, B<PERSON>erWindow, ipcMain, shell } = require('electron');
const path = require('path');

// ============================================
// API助手类 - 处理HTTP请求
// ============================================
class APIHelper {
  // 注册API调用
  static async register(email, password) {
    try {
      console.log(`开始注册请求: ${email}`);
      
      const response = await fetch('https://coke.buyzur.com/api/v1/passport/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        body: JSON.stringify({
          email: email,
          password: password,
          invite_code: "",
          email_code: "",
          recaptcha_data: ""
        })
      });

      const result = await response.json();
      console.log('注册响应:', result);

      if (response.ok && result.data) {
        return {
          success: true,
          message: '注册成功',
          data: result.data
        };
      } else {
        return {
          success: false,
          message: result.message || '注册失败',
          error: result
        };
      }
    } catch (error) {
      console.error('注册请求出错:', error);
      return {
        success: false,
        message: '网络请求失败: ' + error.message,
        error: error
      };
    }
  }

  // 登录API调用
  static async login(email, password) {
    try {
      console.log(`开始登录请求: ${email}`);
      
      const response = await fetch('https://coke.buyzur.com/api/v1/passport/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        body: JSON.stringify({
          email: email,
          password: password,
          captchaData: ""
        })
      });

      const result = await response.json();
      console.log('登录响应:', result);

      if (response.ok && result.data) {
        return {
          success: true,
          message: '登录成功',
          data: result.data
        };
      } else {
        return {
          success: false,
          message: result.message || '登录失败',
          error: result
        };
      }
    } catch (error) {
      console.error('登录请求出错:', error);
      return {
        success: false,
        message: '网络请求失败: ' + error.message,
        error: error
      };
    }
  }

  // 生成Clash订阅链接
  static generateClashLink(token) {
    try {
      if (!token) {
        throw new Error('Token为空');
      }
      
      const subscribeUrl = `https://su.gwzxwk.com/api/v1/client/subscribe?token=${token}`;
      const encodedUrl = encodeURIComponent(subscribeUrl);
      const clashLink = `clash://install-config?url=${encodedUrl}&name=𝘾𝙤𝙠𝙚𝘾𝙡𝙤𝙪𝙙`;
      
      console.log('生成的Clash链接:', clashLink);
      return clashLink;
    } catch (error) {
      console.error('生成Clash链接出错:', error);
      throw error;
    }
  }

  // 自动打开Clash链接
  static async openClashLink(clashLink) {
    try {
      console.log('尝试打开Clash链接:', clashLink);
      
      const { exec } = require('child_process');
      const isWindows = process.platform === 'win32';
      
      if (isWindows) {
        return new Promise((resolve) => {
          exec(`start "" "${clashLink}"`, (error, stdout, stderr) => {
            if (error) {
              console.error('Windows系统命令启动失败:', error);
              resolve({
                success: false,
                message: '无法打开Clash客户端，请确保Clash已安装并正确配置',
                details: { error: error.message, link: clashLink }
              });
            } else {
              console.log('Windows系统命令启动成功');
              resolve({
                success: true,
                message: 'Clash订阅链接已成功打开',
                details: { method: 'windows_start_command', link: clashLink }
              });
            }
          });
        });
      } else {
        // 非Windows系统使用shell.openExternal
        await shell.openExternal(clashLink);
        return {
          success: true,
          message: 'Clash订阅链接已发送给系统',
          details: { method: 'shell.openExternal', link: clashLink }
        };
      }
    } catch (error) {
      console.error('打开Clash链接失败:', error);
      return {
        success: false,
        message: '无法打开Clash客户端: ' + error.message,
        details: { error: error.message, link: clashLink }
      };
    }
  }

  // 完整的自动化流程
  static async autoRegisterLoginSubscribe(email, password) {
    const results = [];
    
    try {
      // 步骤1: 尝试注册
      console.log('=== 开始自动化流程 ===');
      results.push({ step: '开始', message: '启动自动化流程', success: true });
      
      const registerResult = await this.register(email, password);
      results.push({ step: '注册', ...registerResult });
      
      // 无论注册成功与否，都尝试登录
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      
      // 步骤2: 尝试登录
      const loginResult = await this.login(email, password);
      results.push({ step: '登录', ...loginResult });
      
      if (loginResult.success && loginResult.data && loginResult.data.token) {
        // 步骤3: 生成并打开Clash链接
        try {
          const clashLink = this.generateClashLink(loginResult.data.token);
          const openResult = await this.openClashLink(clashLink);
          results.push({ step: 'Clash订阅', ...openResult, clashLink });
          
          return {
            success: true,
            message: '自动化流程完成！注册、登录、Clash订阅全部成功',
            results: results,
            finalData: {
              email,
              token: loginResult.data.token,
              clashLink
            }
          };
        } catch (clashError) {
          results.push({ 
            step: 'Clash订阅', 
            success: false, 
            message: 'Clash链接生成失败: ' + clashError.message 
          });
          
          return {
            success: false,
            message: '登录成功但Clash订阅失败',
            results: results
          };
        }
      } else {
        return {
          success: false,
          message: '登录失败，无法获取订阅token',
          results: results
        };
      }
    } catch (error) {
      console.error('自动化流程出错:', error);
      results.push({ 
        step: '系统错误', 
        success: false, 
        message: error.message 
      });
      
      return {
        success: false,
        message: '自动化流程出错: ' + error.message,
        results: results
      };
    }
  }
}

// 创建主窗口
const createWindow = () => {
  const win = new BrowserWindow({
    width: 330,  // 从450px减少到420px
    height: 450, // 从600px减少到580px
    title: 'Clash 订阅助手', // 窗口标题
    // icon: path.join(__dirname, 'assets/icon.png'), // 应用图标 (如需要请取消注释并添加图标文件)
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // 允许跨域请求
      devTools: true
    },
    resizable: true,
    minWidth: 330,
    minHeight: 450,
    center: true // 窗口居中显示
  });

  win.loadFile('index.html');
  
  // 开发环境下打开开发者工具
  if (process.env.NODE_ENV === 'development') {
  win.webContents.openDevTools();
    }
  
  return win;
};

// 设置IPC处理程序
function setupIPC(mainWindow) {
  // 自动注册+登录+订阅处理
  ipcMain.on('auto-register-login', async (_, data) => {
    try {
      console.log(`收到自动化请求，邮箱: ${data.email}`);
      mainWindow.webContents.send('status-update', '开始自动化流程...');
      
      // 执行完整的自动化流程
      const result = await APIHelper.autoRegisterLoginSubscribe(data.email, data.password);
      
      // 发送详细的结果信息
      console.log('自动化流程完成:', result);
      mainWindow.webContents.send('operation-result', result);
      
      // 如果有详细步骤，逐个发送状态更新
      if (result.results) {
        result.results.forEach((stepResult, index) => {
          setTimeout(() => {
            const statusMessage = `${stepResult.step}: ${stepResult.message}`;
            mainWindow.webContents.send('status-update', statusMessage);
          }, index * 500); // 每500ms发送一个状态更新
        });
            }
            
    } catch (error) {
      console.error('自动化流程出错:', error);
      mainWindow.webContents.send('operation-result', { 
        success: false, 
        message: '自动化流程出错: ' + error.message 
      });
          }
  });

  // 重试打开Clash链接
  ipcMain.on('retry-open-clash', async (_, clashLink) => {
    try {
      console.log('用户手动重试打开Clash链接:', clashLink);
      mainWindow.webContents.send('status-update', '正在重试打开Clash客户端...');
      
      const result = await APIHelper.openClashLink(clashLink);
      console.log('重试结果:', result);
      
      mainWindow.webContents.send('operation-result', {
        success: result.success,
        message: '重试完成: ' + result.message,
        details: result.details
      });
      
    } catch (error) {
      console.error('重试打开Clash链接失败:', error);
      mainWindow.webContents.send('operation-result', { 
        success: false, 
        message: '重试失败: ' + error.message 
      });
    }
  });
}

app.whenReady().then(() => {
  const mainWindow = createWindow();
  setupIPC(mainWindow);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 处理证书错误（用于开发环境）
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  event.preventDefault();
  callback(true);
});