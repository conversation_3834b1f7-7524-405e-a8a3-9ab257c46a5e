# Tool VPN - Clash 订阅助手

一个基于 Electron 的桌面应用，用于自动化 VPN 订阅管理。

## 🚀 快速开始

### 开发模式运行
```bash
npm start
```

### 编译打包
```bash
bash manual-build.sh
```

编译后的应用位于 `dist/my-electron-app-win32-x64/` 目录。

## 📁 项目结构

```
tool-vpn/
├── index.js          # 主进程文件
├── index.html        # 界面文件
├── preload.js        # 预加载脚本
├── renderer.js       # 渲染进程脚本
├── package.json      # 项目配置
├── manual-build.sh   # 编译脚本
├── dist/             # 编译输出目录
└── node_modules/     # 依赖包
```

## 🔧 功能特性

- 用户注册和登录
- 自动化流程处理
- Clash 订阅链接生成
- 自动打开 Clash 客户端

## 📦 技术栈

- Electron v37.2.4
- Node.js
- HTML/CSS/JavaScript
