Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = fso.GetAbsolutePathName(".")

' 设置应用路径
appPath = currentDir & "\my-electron-app-win32-x64\my-electron-app.exe"

' 获取桌面路径
desktopPath = WshShell.SpecialFolders("Desktop")

' 创建快捷方式
Set shortcut = WshShell.CreateShortcut(desktopPath & "\My Electron App.lnk")
shortcut.TargetPath = appPath
shortcut.WorkingDirectory = currentDir & "\my-electron-app-win32-x64"
shortcut.Description = "My Electron App Desktop Application"
shortcut.Save

WScript.Echo "Desktop shortcut created successfully!"
WScript.Echo "Shortcut location: " & desktopPath & "\My Electron App.lnk"
WScript.Echo "Target application: " & appPath
