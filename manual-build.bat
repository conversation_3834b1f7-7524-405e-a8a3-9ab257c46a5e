@echo off
echo 开始手动打包 Electron 应用...

:: 创建输出目录
if not exist "dist" mkdir dist
if not exist "dist\my-electron-app-win32-x64" mkdir dist\my-electron-app-win32-x64

echo 复制 Electron 运行时...
xcopy "node_modules\electron\dist\*" "dist\my-electron-app-win32-x64\" /E /I /Y

echo 复制应用文件...
copy "index.js" "dist\my-electron-app-win32-x64\resources\app\"
copy "index.html" "dist\my-electron-app-win32-x64\resources\app\"
copy "preload.js" "dist\my-electron-app-win32-x64\resources\app\"
copy "renderer.js" "dist\my-electron-app-win32-x64\resources\app\"
copy "package.json" "dist\my-electron-app-win32-x64\resources\app\"

echo 重命名可执行文件...
ren "dist\my-electron-app-win32-x64\electron.exe" "my-electron-app.exe"

echo 打包完成！
echo 可执行文件位置: dist\my-electron-app-win32-x64\my-electron-app.exe
pause
