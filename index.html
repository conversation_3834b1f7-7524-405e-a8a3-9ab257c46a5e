<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clash 订阅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .container {
            display: flex;
            height: 100vh;
            position: relative;
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            justify-content: center;
            align-items: center;
            padding: 0;
            margin: 0;
        }

        .input-panel {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            border-radius: 0;
            box-shadow: none;
        }

        .log-toggle-btn {
            position: fixed;
            bottom: 15px;
            right: 15px;
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 1001;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }

        .log-toggle-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        .log-toggle-btn.active {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .logo {
            text-align: center;
            margin-bottom: 15px;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 3px;
        }

        .logo p {
            font-size: 10px;
            color: #666;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 12px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-with-button {
            display: flex;
            gap: 6px;
            align-items: stretch;
        }

        .input-with-button input {
            flex: 1;
        }

        .btn-small {
            width: 80px;
            padding: 10px 4px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 0;
            white-space: nowrap;
        }

        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-small:active {
            transform: translateY(0);
        }

        .btn-small:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn {
            width: 100%;
            padding: 11px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 6px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            color: #2d5a27;
            margin-top: 3px;
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(168, 237, 234, 0.3);
        }

        .status {
            margin-top: 12px;
            padding: 8px;
            border-radius: 5px;
            font-size: 11px;
            text-align: center;
            min-height: 15px;
        }

        .status.working {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }

        .progress-bar {
            width: 100%;
            height: 3px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-panel {
            width: 320px;
            background: #1a1a1a;
            color: #e0e0e0;
            padding: 10px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 999;
            box-shadow: -8px 0 25px rgba(0, 0, 0, 0.4);
            border-left: 2px solid #333;
        }

        .log-panel.show {
            transform: translateX(0);
        }

        .log-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #333;
            position: sticky;
            top: 0;
            background: #1a1a1a;
            z-index: 10;
        }

        .log-panel-close {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: #ccc;
            font-size: 14px;
            cursor: pointer;
            padding: 5px 7px;
            border-radius: 5px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 26px;
            height: 26px;
            font-weight: bold;
        }

        .log-panel-close:hover {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            transform: scale(1.1);
        }

        .log-header {
            color: #667eea;
            font-size: 13px;
            font-weight: 600;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 10px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .log-entry.info {
            background: rgba(102, 126, 234, 0.12);
            border-left: 3px solid #667eea;
        }

        .log-entry.success {
            background: rgba(76, 175, 80, 0.12);
            border-left: 3px solid #4caf50;
        }

        .log-entry.error {
            background: rgba(244, 67, 54, 0.12);
            border-left: 3px solid #f44336;
        }

        .log-entry.warning {
            background: rgba(255, 152, 0, 0.12);
            border-left: 3px solid #ff9800;
        }

        .log-time {
            color: #888;
            font-size: 8px;
            margin-right: 4px;
            opacity: 0.8;
        }

        .log-panel::-webkit-scrollbar {
            width: 4px;
        }

        .log-panel::-webkit-scrollbar-track {
            background: #2a2a2a;
        }

        .log-panel::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 2px;
        }

        .log-panel::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        .clash-link-container {
            margin: 8px 0;
            padding: 8px;
            background: rgba(102, 126, 234, 0.08);
            border-radius: 5px;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .clash-link-header {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .clash-link-content {
            background: #2a2a2a;
            padding: 5px;
            border-radius: 3px;
            color: #e0e0e0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 9px;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #444;
            max-height: 60px;
            overflow-y: auto;
        }

        .clash-link-content:hover {
            background: #333;
            border-color: #667eea;
        }

        .clash-link-actions {
            margin-top: 5px;
            text-align: center;
        }

        .retry-clash-btn {
            padding: 4px 8px;
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            border: none;
            border-radius: 3px;
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .retry-clash-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .clash-link-instructions {
            margin-top: 6px;
            padding: 6px;
            background: rgba(76, 175, 80, 0.08);
            border-radius: 3px;
            border-left: 3px solid #4caf50;
        }

        .instruction-title {
            color: #4caf50;
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 10px;
        }

        .instruction-steps {
            font-size: 9px;
            color: #ccc;
            line-height: 1.3;
        }

        .instruction-steps div {
            margin-bottom: 2px;
            padding-left: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="input-panel">
            <div class="logo">
                <h1> 自动化工具</h1>
                <p>注册  登录  Clash订阅</p>
            </div>

            <div class="form-group">
                <label for="email">邮箱地址</label>
                <div class="input-with-button">
                    <input type="email" id="email" placeholder="请输入邮箱地址或点击自动生成">
                    <button id="generate-email-btn" class="btn-small" type="button">
                         自动生成
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="请输入密码">
            </div>

            <button id="auto-register-login-btn" class="btn">
                 自动注册+登录+订阅
            </button>

            <button id="one-click-btn" class="btn btn-secondary">
                 一键生成并执行
            </button>

            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>

            <div id="status" class="status"></div>
        </div>

        <button id="log-toggle-btn" class="log-toggle-btn" title="查看日志">
            
        </button>

        <div class="log-panel" id="log-panel">
            <div class="log-panel-header">
                <div class="log-header"> 操作日志</div>
                <button class="log-panel-close" id="log-panel-close-btn" title="关闭日志">
                    
                </button>
            </div>
            <div id="log-container"></div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
