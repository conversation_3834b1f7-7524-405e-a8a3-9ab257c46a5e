# 🎨 应用程序名称和图标配置指南

## 📝 应用程序名称更改

### 1. 窗口标题
**文件位置：** `index.html` 第6行
```html
<title>自动注册登录 - Clash订阅</title>
```
**修改为：**
```html
<title>您的应用名称</title>
```

### 2. 应用包名
**文件位置：** `package.json` 第2行
```json
"name": "my-electron-app"
```
**修改为：**
```json
"name": "your-app-name"
```

### 3. 窗口标题栏
**文件位置：** `index.js` 第241行（已添加）
```javascript
title: 'My Electron App'
```
**修改为：**
```javascript
title: '您的应用名称'
```

### 4. 打包后的可执行文件名
**文件位置：** `package.json` 第8行 和 `manual-build.sh`
- 在 package.json 中修改构建脚本的应用名
- 在 manual-build.sh 中修改重命名的文件名

## 🎨 应用程序图标设置

### 1. 图标文件准备
创建以下图标文件并放入 `assets/` 目录：
- `icon.png` - 主图标 (推荐 256x256 或 512x512)
- `icon.ico` - Windows 图标 (可选，用于更好的 Windows 支持)

### 2. 图标配置
**文件位置：** `index.js` 第242行（已添加）
```javascript
icon: path.join(__dirname, 'assets/icon.png')
```

### 3. 图标格式要求
- **PNG格式：** 推荐，支持透明背景
- **ICO格式：** Windows 原生支持
- **尺寸：** 256x256 或 512x512 像素
- **透明背景：** 推荐使用透明背景

## 🔧 完整修改步骤

### 步骤1：准备图标文件
1. 创建或获取应用图标（PNG格式，256x256像素）
2. 将图标文件命名为 `icon.png`
3. 放入项目根目录的 `assets/` 文件夹

### 步骤2：修改应用名称
1. 修改 `index.html` 中的 `<title>` 标签
2. 修改 `package.json` 中的 `name` 字段
3. 修改 `index.js` 中的 `title` 属性

### 步骤3：重新编译
运行以下命令重新编译应用：
```bash
bash manual-build.sh
```

### 步骤4：更新快捷方式
重新创建桌面快捷方式以应用新的图标：
```bash
双击 "创建桌面快捷方式.bat"
```

## 📁 文件结构示例
```
my-electron-app/
├── assets/
│   ├── icon.png          # 主图标文件
│   └── icon.ico          # Windows 图标（可选）
├── index.html            # 修改 <title>
├── index.js              # 修改 title 和 icon 路径
├── package.json          # 修改 name
└── manual-build.sh       # 重新编译脚本
```

## 💡 提示
- 图标文件路径必须正确，否则会使用系统默认图标
- 修改后需要重新编译才能看到效果
- 建议使用高质量的 PNG 图标以获得最佳显示效果
- 可以为不同平台准备不同格式的图标文件
