const { contextBridge, ipcRenderer } = require('electron');

// 暴露给渲染进程的简化API
contextBridge.exposeInMainWorld('electronAPI', {
  // 自动注册+登录+订阅
  autoRegisterLogin: (data) => ipcRenderer.send('auto-register-login', data),
  
  // 重试打开Clash链接
  retryOpenClash: (clashLink) => {
    // 增加重试计数
    if (!window.clashRetryCount) {
      window.clashRetryCount = 0;
    }
    window.clashRetryCount++;
    
    ipcRenderer.send('retry-open-clash', clashLink);
  },
  
  // 获取重试计数
  getRetryCount: () => window.clashRetryCount || 0,
  
  // 重置重试计数
  resetRetryCount: () => { window.clashRetryCount = 0; },
  
  // 获取操作状态
  onStatusUpdate: (callback) => ipcRenderer.on('status-update', (_, status) => callback(status)),
  
  // 获取操作结果
  onOperationResult: (callback) => ipcRenderer.on('operation-result', (_, result) => callback(result))
});

// 注入自动化辅助函数
window.addEventListener('DOMContentLoaded', () => {
  console.log('🚀 简化版自动化工具已加载');
}); 